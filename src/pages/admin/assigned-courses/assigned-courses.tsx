import classNamesBind from 'classnames/bind'
import React, { useState, useRef, useCallback, useMemo } from 'react'
import { v4 as uuid } from 'uuid'
import { QueryStatus } from '@reduxjs/toolkit/query'
import styles from './assigned-courses.module.scss'
import { Pagination, Button, HelpIcon } from '@/shared/ui'
import { assignedCoursesApi, CourseTabVariants, Course as AssignedCourse } from './api'
import { sharedCoursesApi } from '@/entities/courses/model/api/shared'
import { SharedCourse } from '@/entities/courses/model/types/courses'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { Tabs } from '@/shared/components'
import { getDateThroughDot } from '@/shared/helpers/date'
import { formatNumber } from '@/shared/helpers'
import { useTranslation } from 'react-i18next'
import { getAssignedCoursesDetailStatisticsByIdUrl, URLS } from '@/shared/configs/urls'
import { LoadingDataStatus } from '@/shared/components/loading-data-status'
import { isCourseByTag } from '@/entities/courses'
import { AssignedCourseCard } from '@/shared/components/assigned-course-card/assigned-course-card'
import { useUserOrganizationId } from '@/entities/employee'
import { useEvent } from '@/shared/hooks'

const cx = classNamesBind.bind(styles)

const LIMIT = 12

const AssignedCourses: React.FC = () => {
  const { t } = useTranslation('pages__learning__assigned-courses')
  const [currentQueryParameters, setSearchParams] = useSearchParams()
  const type: CourseTabVariants =
    (currentQueryParameters.get('type') as CourseTabVariants) || 'active'

  const [page, setPage] = useState(1)
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const navigate = useNavigate()
  const menuRef = useRef<HTMLDivElement>(null)

  const handleClick = (value: string) => {
    if (!value) return
    const newQueryParameters: URLSearchParams = new URLSearchParams()

    newQueryParameters.set('type', value)
    setPage(1)
    setSearchParams(newQueryParameters)
  }
  const userOrganizationId = useUserOrganizationId()

  const {
    data: assignedCourses,
    status: assignedStatus,
    isLoading: isAssignedLoading,
    isFetching: isAssignedFetching,
  } = assignedCoursesApi.useGetAssignedCoursesByOrganizationQuery(
    {
      organization_id: userOrganizationId ?? '',
      limit: LIMIT,
      status: type,
      offset: (page - 1) * LIMIT,
    },
    {
      skip: !userOrganizationId || type === 'shared',
    },
  )

  const {
    data: sharedCourses,
    status: sharedStatus,
    isLoading: isSharedLoading,
    isFetching: isSharedFetching,
  } = sharedCoursesApi.useGetSharedCoursesByOrganizationIdQuery(
    {
      organization_id: userOrganizationId ?? '',
      limit: LIMIT,
      status: 'active',
      offset: (page - 1) * LIMIT,
    },
    {
      skip: !userOrganizationId || type !== 'shared',
    },
  )

  const {
    data: completedSharedCourses,
    status: completedSharedStatus,
    isLoading: isCompletedSharedLoading,
    isFetching: isCompletedSharedFetching,
  } = sharedCoursesApi.useGetSharedCoursesByOrganizationIdQuery(
    {
      organization_id: userOrganizationId ?? '',
      limit: LIMIT,
      status: 'completed',
      offset: (page - 1) * LIMIT,
    },
    {
      skip: !userOrganizationId || type !== 'completed',
    },
  )

  const combinedCompletedCourses = useMemo(() => {
    if (type !== 'completed') return null

    const assignedData = assignedCourses?.data || []
    const sharedData = completedSharedCourses?.data || []

    return {
      data: [...assignedData, ...sharedData],
      total_count: (assignedCourses?.total_count || 0) + (completedSharedCourses?.total_count || 0),
    }
  }, [type, assignedCourses, completedSharedCourses])

  const courses =
    type === 'shared'
      ? sharedCourses
      : type === 'completed'
        ? combinedCompletedCourses
        : assignedCourses

  const status =
    type === 'shared'
      ? sharedStatus
      : type === 'completed'
        ? assignedStatus === QueryStatus.fulfilled &&
          completedSharedStatus === QueryStatus.fulfilled
          ? QueryStatus.fulfilled
          : assignedStatus === QueryStatus.rejected ||
              completedSharedStatus === QueryStatus.rejected
            ? QueryStatus.rejected
            : QueryStatus.pending
        : assignedStatus

  const isLoading =
    type === 'shared'
      ? isSharedLoading
      : type === 'completed'
        ? isAssignedLoading || isCompletedSharedLoading
        : isAssignedLoading

  const isFetching =
    type === 'shared'
      ? isSharedFetching
      : type === 'completed'
        ? isAssignedFetching || isCompletedSharedFetching
        : isAssignedFetching

  const onSetPage = (page: number) => {
    setPage(page + 1)
  }

  const getCourseData = (course: AssignedCourse | SharedCourse): AssignedCourse => {
    if (type === 'shared') {
      return (course as SharedCourse).assigned_course
    } else if (type === 'completed' && 'assigned_course' in course) {
      return (course as SharedCourse).assigned_course
    } else {
      return course as AssignedCourse
    }
  }

  const handleRegularCourse = () => {
    navigate(URLS.ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PAGE)
    setIsMenuOpen(false)
  }

  const handlePublicCourse = () => {
    navigate(URLS.ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PUBLIC_PAGE)
    setIsMenuOpen(false)
  }

  const handleMenuToggle = () => {
    setIsMenuOpen(prev => !prev)
  }

  const handleOutsideClick = useCallback((e: Event) => {
    if (!menuRef.current) return
    if (e.composedPath().indexOf(menuRef.current) === -1) {
      setIsMenuOpen(false)
    }
  }, [])

  useEvent('click', handleOutsideClick, window)

  const menuItems = [
    {
      name: 'regular',
      text: t('regular_course'),
      onClick: handleRegularCourse,
      tooltip: t('regular_course_tooltip'),
    },
    {
      name: 'public',
      text: t('public_course'),
      onClick: handlePublicCourse,
      tooltip: t('public_course_tooltip'),
    },
  ]

  const [tabs] = useState<
    Array<{
      value: string
      name: string
    }>
  >([
    {
      value: t('tabs.active'),
      name: 'active',
    },
    {
      value: t('tabs.completed'),
      name: 'completed',
    },
    {
      value: t('tabs.planned'),
      name: 'planned',
    },
    {
      value: t('tabs.shared'),
      name: 'shared',
    },
  ])

  return (
    <section className={cx('page')}>
      <div className={cx('actions')}>
        <h1 className={cx('title')}>{t('title')}</h1>
        <div className={cx('menu-wrapper')} ref={menuRef}>
          <Button onClick={handleMenuToggle}>{t('assign_course')}</Button>
          {isMenuOpen && (
            <div className={cx('dropdown-menu')}>
              {menuItems.map(item => (
                <div key={item.name} className={cx('menu-item')} onClick={item.onClick}>
                  <span>{item.text}</span>
                  <HelpIcon text={item.tooltip} tooltipClassname={cx('tooltip')} />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      <div className={cx('content')}>
        <Tabs tabs={tabs} onClick={handleClick} active={type} />
        <LoadingDataStatus
          data={courses?.data}
          fetchStatus={status}
          createHandler={() => {}}
          dataLength={courses?.data?.length}
          showCreateHandler={false}
          texts={{
            empty: t('plug.empty'),
          }}
        />
        <ul className={cx('courses')}>
          {courses?.data?.map(course => {
            const courseData = getCourseData(course)

            const detailUrl =
              type === 'shared' || (type === 'completed' && 'assigned_course' in course)
                ? // eslint-disable-next-line i18next/no-literal-string
                  `${getAssignedCoursesDetailStatisticsByIdUrl(courseData.id)}?shared_id=${course.id}`
                : getAssignedCoursesDetailStatisticsByIdUrl(courseData.id)

            return (
              <li key={courseData.id}>
                <AssignedCourseCard
                  to={detailUrl}
                  title={courseData.title}
                  pictureSrc={courseData.picture}
                  footerCols={[
                    {
                      id: uuid(),
                      title: (
                        <>
                          {formatNumber(courseData.employees_count)}{' '}
                          {t('commons:people_without_count', { count: courseData.employees_count })}
                        </>
                      ),
                      value: (
                        <>
                          <time>{getDateThroughDot(new Date(courseData.start_date))}</time>
                          {!isCourseByTag(courseData.end_date) && type !== 'shared' && (
                            <>
                              <span> --- </span>
                              <time>{getDateThroughDot(new Date(courseData.end_date))}</time>
                            </>
                          )}
                        </>
                      ),
                    },
                  ]}
                />
              </li>
            )
          })}
        </ul>
        <Pagination
          currentPage={(+page || 1) - 1}
          limit={LIMIT}
          total={courses?.total_count || 0}
          onChange={onSetPage}
          isLoading={isLoading || isFetching}
        />
      </div>
    </section>
  )
}

export default AssignedCourses
