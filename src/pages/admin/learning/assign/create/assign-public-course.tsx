import { useTranslation } from 'react-i18next'
import { <PERSON>readc<PERSON>bs, Button, Checkbox, Input, Textarea, Tooltip } from '@/shared/ui'
import EditBoldIcon from '@/shared/ui/Icon/icons/components/EditBoldIcon'
import TrashBoldIcon from '@/shared/ui/Icon/icons/components/TrashBoldIcon'
import ArticleWarningIcon from '@/shared/ui/Icon/icons/components/ArticleWarningIcon'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import styles from './assign-course.module.scss'
import classNamesBind from 'classnames/bind'
import { useEffect, useMemo, useState } from 'react'
import { URLS } from '@/shared/configs/urls'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { useAppDispatch, useAppSelector } from '@/store'
import { useUnmountOrganizationTree } from '@/shared/modals/organization-tree'
import {
  onChangeSelectedCourseById,
  onDeleteFromSelectedCourses,
  resetFullStateForm,
  selectForm,
  selectSelectedCourses,
  setForm,
} from './slice'
import { v4 as uuid } from 'uuid'
import { CourseCard } from './course-card'
import { useNavigate } from 'react-router-dom'
import { zodResolver } from '@hookform/resolvers/zod'
import { getAssignPublicCourseSchema } from './resolver'
import { useNotification } from '@/shared/contexts/notifications'
import { CourseForAssign, coursesApi } from '@/entities/courses'
import { sharedCoursesApi } from '@/entities/courses/model/api/shared'
import ImageUpload from '@/shared/components/image-upload'

const cx = classNamesBind.bind(styles)

export type FormValues = {
  notifications: {
    assign: boolean
  }
  image?: File | string
  courses: CourseForAssign[]
}

export const Create = () => {
  const { t } = useTranslation('pages__learning__assign-course')
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const selectedCourses = useAppSelector(selectSelectedCourses)
  const [shareCourse, { isLoading: isShareCourseLoading }] =
    sharedCoursesApi.useShareCourseMutation()
  const [attachPicture, { isLoading: isAttachCourseLoading }] =
    coursesApi.useAttachPictureToCourseMutation()
  const storeForm = useAppSelector(selectForm)
  const { add } = useNotification()
  const schema = useMemo(
    () =>
      getAssignPublicCourseSchema({
        coursesRequiredMsg: t('validation.courses_required'),
      }),
    [t],
  )
  const isLoading = isShareCourseLoading || isAttachCourseLoading
  const [editingCourse, setEditingCourse] = useState<string>()
  useUnmountOrganizationTree()

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: storeForm,
  })

  useEffect(() => {
    form.setValue(
      'courses',
      selectedCourses?.map(c => ({
        course_id: c.id,
        title: c.title,
        description: c.description || '',
      })),
      {
        shouldValidate: true,
      },
    )
  }, [form, selectedCourses])

  const breadcrumbItems = useMemo(
    () => [
      {
        id: URLS.ADMIN_LEARNING_PAGE,
        text: t('breadcrumbs.assigned_courses'),
        clickable: true,
      },
      {
        id: URLS.ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PUBLIC_PAGE,
        text: t('breadcrumbs.assigning'),
        clickable: true,
      },
      {
        id: URLS.ADMIN_CREATE_COURSE_PAGE,
        text: t('breadcrumbs.assign_course_public'),
        clickable: false,
      },
    ],
    [t],
  )

  const isEmptyCoursesSelected = useMemo(
    () => selectedCourses.some(selectedCourse => selectedCourse?.themes_count === 0),
    [selectedCourses],
  )

  const onSubmit: SubmitHandler<FormValues> = async data => {
    if (isEmptyCoursesSelected) {
      add({
        id: uuid(),
        status: 'error',
        message: t('validation.no_themes'),
      })
      return
    }

    if (data?.courses?.length === 1) {
      const course = data?.courses?.[0]

      const courseData = await shareCourse({
        course_id: course.course_id,
        title: course.title,
        description: course.description || '',
        need_assigned_message: !!data.notifications?.assign,
      }).unwrap()

      if (data.image && data.image instanceof File) {
        await attachPicture({
          assigned_course_id: courseData?.assigned_course_id,
          image: data.image,
        }).unwrap()
      }

      dispatch(resetFullStateForm())
      form.reset()
      navigate(URLS.ADMIN_LEARNING_PAGE)
      add({
        message: t('success'),
        status: 'success',
        id: uuid(),
      })
      return
    }
  }

  return (
    <div className={cx('page')}>
      <div className={cx('header')}>
        <Breadcrumbs items={breadcrumbItems} className={cx('breadcrumbs')} />
        <h1 className={cx('title')}>{t('title_public')}</h1>
      </div>
      <form onSubmit={form.handleSubmit(onSubmit)} className={cx('form')}>
        <div className={cx('form__content')}>
          <div className={cx('choose')}>
            <h3 className={cx('title_small')}>{t('form.choose_course')}</h3>
            {selectedCourses?.length > 0 && (
              <Controller
                name='courses'
                control={form.control}
                render={({ fieldState }) => (
                  <>
                    <ul className={cx('courses', 'scrollbar')}>
                      {selectedCourses.map(course => (
                        <CourseCard
                          key={course.id}
                          course={course}
                          content={
                            course?.id === editingCourse ? (
                              <div className={cx('courses__item__edit')}>
                                <Input
                                  fullWidth
                                  value={course.title}
                                  onChange={v =>
                                    dispatch(
                                      onChangeSelectedCourseById({
                                        id: course.id,
                                        course: { ...course, title: v },
                                      }),
                                    )
                                  }
                                  label={t('form.editing.title')}
                                />
                                <Textarea
                                  className={cx('text-area')}
                                  fullWidth
                                  value={course.description ?? ''}
                                  onChange={v =>
                                    dispatch(
                                      onChangeSelectedCourseById({
                                        id: course.id,
                                        course: { ...course, description: v },
                                      }),
                                    )
                                  }
                                  label={t('form.editing.description')}
                                />
                              </div>
                            ) : null
                          }
                          wrapperClassName={
                            course?.id === editingCourse
                              ? cx('courses__item__edit__wrapper')
                              : undefined
                          }
                          contentClassName={cx('courses__item__content')}
                          endActions={
                            <div className={cx('courses__actions')}>
                              <IconWrapper
                                onClick={() =>
                                  setEditingCourse(
                                    course?.id === editingCourse ? undefined : course?.id,
                                  )
                                }
                                className={cx('courses__actions__item')}
                                color={course?.id === editingCourse ? 'gray90' : 'gray70'}
                              >
                                <EditBoldIcon />
                              </IconWrapper>
                              <IconWrapper
                                onClick={() => {
                                  setEditingCourse(undefined)
                                  dispatch(onDeleteFromSelectedCourses(course?.id))
                                }}
                                className={cx('courses__actions__item')}
                                color='gray70'
                              >
                                <TrashBoldIcon />
                              </IconWrapper>
                              {course?.themes_count === 0 && (
                                <Tooltip
                                  content={t('tooltip.no_themes')}
                                  tooltipClassname={cx('noThemesTooltip')}
                                >
                                  <IconWrapper className={cx('courses__actions__item')} color='red'>
                                    <ArticleWarningIcon />
                                  </IconWrapper>
                                </Tooltip>
                              )}
                            </div>
                          }
                        />
                      ))}
                    </ul>
                    {fieldState.error?.message}
                  </>
                )}
              />
            )}
            <div>
              {selectedCourses.length === 0 && (
                <Button
                  onClick={() => navigate(URLS.ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PUBLIC_PAGE)}
                  type='button'
                  size='small'
                  color={form.formState.errors.courses?.message ? 'red' : 'darkGray'}
                >
                  <IconWrapper
                    size='16'
                    color={form.formState.errors.courses?.message ? 'white' : 'gray70'}
                  >
                    <PlusIcon />
                  </IconWrapper>
                  <span>{t('form.add_course')}</span>
                </Button>
              )}
              {form.formState.errors?.courses?.message && (
                <p className='error-text'>{form.formState.errors?.courses?.message}</p>
              )}
            </div>
          </div>

          <div className={cx('notifications')}>
            <h3 className={cx('title_small')}>{t('form.notifications.title')}</h3>
            <Controller
              name='notifications'
              control={form.control}
              render={({ field }) => (
                <ul className={cx('notifications__list')}>
                  <li>
                    <Checkbox
                      customChecked={!!field.value?.['assign']}
                      onChange={v => {
                        field.onChange({ assign: v })
                        dispatch(setForm({ notifications: { assign: v, lagging: false } }))
                      }}
                      label={<p>{t('form.notifications.assign')}</p>}
                    />
                  </li>
                </ul>
              )}
            />
          </div>
        </div>
        {selectedCourses?.length === 1 && (
          <div className={cx('form__image')}>
            <Controller
              control={form.control}
              name='image'
              render={({ field }) => <ImageUpload onSelect={field.onChange} />}
            />
          </div>
        )}
        <div className={cx('actions')}>
          <Button
            onClick={() => navigate(URLS.ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PUBLIC_PAGE)}
            type='button'
            size='big'
            color='gray'
          >
            {t('actions.back')}
          </Button>
          <Button disabled={!form.formState.isValid || isLoading} type='submit' size='big'>
            {t('actions.assing')}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default Create
